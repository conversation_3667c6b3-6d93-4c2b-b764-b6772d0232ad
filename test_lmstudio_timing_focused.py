#!/usr/bin/env python3

import requests
import json
import time
import statistics
import base64

# Focused timing tests for LM Studio
base_url = "http://127.0.0.1:1234"

def encode_image(image_path):
    """Encode image to base64"""
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def test_text_only_timing():
    """Test text-only responses with timing"""
    
    print("="*60)
    print("TEXT-ONLY TIMING TEST")
    print("="*60)
    
    url = f"{base_url}/v1/chat/completions"
    
    prompts = [
        "Hello, how are you?",
        "What is 2+2?",
        "Write a haiku about coding.",
        "Explain AI in one sentence.",
        "What's the weather like today?"
    ]
    
    times = []
    
    for i, prompt in enumerate(prompts, 1):
        payload = {
            "model": "llava-1.6-mistral-7b",
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.7,
            "max_tokens": 100
        }
        
        print(f"\nTest {i}: {prompt}")
        
        try:
            start_time = time.time()
            response = requests.post(url, headers={"Content-Type": "application/json"}, 
                                   data=json.dumps(payload), timeout=60)
            end_time = time.time()
            
            duration = end_time - start_time
            times.append(duration)
            
            if response.status_code == 200:
                result = response.json()
                tokens = result.get('usage', {}).get('total_tokens', 'N/A')
                print(f"✅ Time: {duration:.2f}s | Tokens: {tokens}")
            else:
                print(f"❌ Failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    if times:
        print(f"\n{'='*60}")
        print("TEXT TIMING RESULTS:")
        print(f"Average: {statistics.mean(times):.2f}s")
        print(f"Min: {min(times):.2f}s")
        print(f"Max: {max(times):.2f}s")
        if len(times) > 1:
            print(f"Std Dev: {statistics.stdev(times):.2f}s")
    
    return times

def test_single_image_timing():
    """Test single image analysis timing"""
    
    print(f"\n{'='*60}")
    print("SINGLE IMAGE TIMING TEST")
    print("="*60)
    
    url = f"{base_url}/v1/chat/completions"
    
    try:
        # Load image once
        image_path = "test_image.jpg"
        base64_image = encode_image(image_path)
        print(f"Image loaded: {image_path}")
        
        payload = {
            "model": "llava-1.6-mistral-7b",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "Describe this image in detail."},
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}
                        }
                    ]
                }
            ],
            "temperature": 0.7,
            "max_tokens": 200
        }
        
        print("\nAnalyzing image...")
        
        start_time = time.time()
        response = requests.post(url, headers={"Content-Type": "application/json"}, 
                               data=json.dumps(payload), timeout=90)
        end_time = time.time()
        
        duration = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            tokens = result.get('usage', {}).get('total_tokens', 'N/A')
            print(f"✅ Image analysis time: {duration:.2f}s | Tokens: {tokens}")
            
            if 'choices' in result:
                message = result['choices'][0]['message']['content']
                print(f"Response preview: {message[:150]}...")
        else:
            print(f"❌ Failed: {response.status_code}")
            
        return duration
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_multiple_questions_same_image():
    """Test multiple questions about the same image - each with image data"""
    
    print(f"\n{'='*60}")
    print("MULTIPLE QUESTIONS - SAME IMAGE")
    print("(Each question includes the image data)")
    print("="*60)
    
    url = f"{base_url}/v1/chat/completions"
    
    questions = [
        "What do you see in this image?",
        "What colors are dominant?",
        "What's the weather like?",
        "Are there any people visible?"
    ]
    
    try:
        # Load image once but send with each request
        image_path = "test_image.jpg"
        base64_image = encode_image(image_path)
        print(f"Image loaded: {image_path}")
        print("Note: Image data sent with EACH question")
        
        times = []
        
        for i, question in enumerate(questions, 1):
            payload = {
                "model": "llava-1.6-mistral-7b",
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": question},
                            {
                                "type": "image_url",
                                "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}
                            }
                        ]
                    }
                ],
                "temperature": 0.3,
                "max_tokens": 150
            }
            
            print(f"\nQ{i}: {question}")
            
            start_time = time.time()
            response = requests.post(url, headers={"Content-Type": "application/json"}, 
                                   data=json.dumps(payload), timeout=90)
            end_time = time.time()
            
            duration = end_time - start_time
            times.append(duration)
            
            if response.status_code == 200:
                result = response.json()
                tokens = result.get('usage', {}).get('total_tokens', 'N/A')
                print(f"✅ Time: {duration:.2f}s | Tokens: {tokens}")
                
                if 'choices' in result:
                    answer = result['choices'][0]['message']['content']
                    print(f"Answer: {answer[:100]}...")
            else:
                print(f"❌ Failed: {response.status_code}")
        
        if times:
            print(f"\n{'='*60}")
            print("MULTIPLE QUESTIONS TIMING:")
            print(f"Average per question: {statistics.mean(times):.2f}s")
            print(f"Total time for all questions: {sum(times):.2f}s")
            print(f"Min: {min(times):.2f}s")
            print(f"Max: {max(times):.2f}s")
        
        return times
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return []

def test_completions_vs_chat():
    """Compare /v1/completions vs /v1/chat/completions"""
    
    print(f"\n{'='*60}")
    print("COMPLETIONS vs CHAT/COMPLETIONS COMPARISON")
    print("="*60)
    
    prompt_text = "The capital of France is"
    
    # Test /v1/completions
    print("\nTesting /v1/completions...")
    try:
        url = f"{base_url}/v1/completions"
        payload = {
            "model": "llava-1.6-mistral-7b",
            "prompt": prompt_text,
            "max_tokens": 50,
            "temperature": 0.3
        }
        
        start_time = time.time()
        response = requests.post(url, headers={"Content-Type": "application/json"}, 
                               data=json.dumps(payload), timeout=60)
        end_time = time.time()
        
        completions_time = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ /v1/completions time: {completions_time:.2f}s")
            if 'choices' in result:
                text = result['choices'][0]['text']
                print(f"Response: {prompt_text}{text}")
        else:
            print(f"❌ /v1/completions failed: {response.status_code}")
            completions_time = None
            
    except Exception as e:
        print(f"❌ /v1/completions error: {e}")
        completions_time = None
    
    # Test /v1/chat/completions
    print("\nTesting /v1/chat/completions...")
    try:
        url = f"{base_url}/v1/chat/completions"
        payload = {
            "model": "llava-1.6-mistral-7b",
            "messages": [{"role": "user", "content": prompt_text}],
            "max_tokens": 50,
            "temperature": 0.3
        }
        
        start_time = time.time()
        response = requests.post(url, headers={"Content-Type": "application/json"}, 
                               data=json.dumps(payload), timeout=60)
        end_time = time.time()
        
        chat_time = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ /v1/chat/completions time: {chat_time:.2f}s")
            if 'choices' in result:
                message = result['choices'][0]['message']['content']
                print(f"Response: {message}")
        else:
            print(f"❌ /v1/chat/completions failed: {response.status_code}")
            chat_time = None
            
    except Exception as e:
        print(f"❌ /v1/chat/completions error: {e}")
        chat_time = None
    
    # Compare
    if completions_time and chat_time:
        print(f"\n{'='*60}")
        print("COMPARISON:")
        print(f"/v1/completions: {completions_time:.2f}s")
        print(f"/v1/chat/completions: {chat_time:.2f}s")
        diff = abs(completions_time - chat_time)
        faster = "completions" if completions_time < chat_time else "chat/completions"
        print(f"Difference: {diff:.2f}s ({faster} is faster)")

if __name__ == "__main__":
    print("LM STUDIO FOCUSED TIMING TESTS")
    print(f"Testing: {base_url}")
    print(f"Time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run focused tests
    text_times = test_text_only_timing()
    single_image_time = test_single_image_timing()
    multiple_questions_times = test_multiple_questions_same_image()
    test_completions_vs_chat()
    
    # Final summary
    print(f"\n{'='*60}")
    print("FINAL TIMING SUMMARY")
    print("="*60)
    
    if text_times:
        print(f"Text-only average: {statistics.mean(text_times):.2f}s")
    
    if single_image_time:
        print(f"Single image analysis: {single_image_time:.2f}s")
    
    if multiple_questions_times:
        print(f"Multiple questions (avg): {statistics.mean(multiple_questions_times):.2f}s")
        print(f"Multiple questions (total): {sum(multiple_questions_times):.2f}s")
    
    print(f"\nCompleted: {time.strftime('%Y-%m-%d %H:%M:%S')}")
