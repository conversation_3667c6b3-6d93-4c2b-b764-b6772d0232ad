#!/usr/bin/env python3

import google.generativeai as genai

# Test the Gemini API key using the official SDK
api_key = "AIzaSyDU16bNRLaMg-Kcr36TTTDFZOpA4E61nMk"

try:
    # Configure the API key
    genai.configure(api_key=api_key)
    
    # List available models
    print("Available models:")
    for model in genai.list_models():
        if 'generateContent' in model.supported_generation_methods:
            print(f"- {model.name}")
    
    print("\n" + "="*50)
    
    # Test with Gemini 1.5 Flash
    print("Testing Gemini 1.5 Flash...")
    model = genai.GenerativeModel('gemini-1.5-flash')
    
    response = model.generate_content("Write a haiku about coding")
    print(f"Response: {response.text}")
    
    print("\n" + "="*50)
    
    # Test with conversation
    print("Testing conversation...")
    chat = model.start_chat(history=[])
    
    response = chat.send_message("Hello! What's your name?")
    print(f"AI: {response.text}")
    
    response = chat.send_message("Can you help me with Python programming?")
    print(f"AI: {response.text}")
    
    print("\n✅ Gemini API Key Test with SDK SUCCESSFUL!")
    
except Exception as e:
    print("❌ Gemini API Key Test with SDK FAILED!")
    print(f"Error: {e}")
