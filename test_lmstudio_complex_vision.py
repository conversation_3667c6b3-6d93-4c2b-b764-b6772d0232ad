#!/usr/bin/env python3

import requests
import json
import base64
from PIL import Image

# Test LM Studio with complex city image
base_url = "http://127.0.0.1:1234"

def encode_image(image_path):
    """Encode image to base64"""
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def test_complex_scene_analysis():
    """Test with the Times Square image"""
    
    url = f"{base_url}/v1/chat/completions"
    
    try:
        # Load and encode the complex city image
        image_path = "city_image.jpg"
        image = Image.open(image_path)
        print(f"Testing with complex city image: {image_path}")
        print(f"Image size: {image.size}")
        
        # Encode image to base64
        base64_image = encode_image(image_path)
        
        payload = {
            "model": "llava-1.6-mistral-7b",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "Analyze this urban scene in detail. What location is this? What can you see? Describe the signs, people, vehicles, and atmosphere."
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            "temperature": 0.7,
            "max_tokens": 400
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        print("\nTesting complex scene analysis...")
        
        response = requests.post(url, headers=headers, data=json.dumps(payload), timeout=90)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Complex Scene Analysis SUCCESSFUL!")
            
            if 'choices' in result and len(result['choices']) > 0:
                message = result['choices'][0]['message']['content']
                print(f"\nScene Analysis: {message}")
                
                if 'usage' in result:
                    print(f"\nUsage: {result['usage']}")
                    
            else:
                print(f"Unexpected response format: {result}")
                
        else:
            print("❌ Complex Scene Analysis FAILED!")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Complex Scene Error: {e}")
        import traceback
        traceback.print_exc()

def test_text_recognition():
    """Test text recognition capabilities"""
    
    url = f"{base_url}/v1/chat/completions"
    
    try:
        image_path = "city_image.jpg"
        base64_image = encode_image(image_path)
        
        payload = {
            "model": "llava-1.6-mistral-7b",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "Can you read and list any text, signs, or advertisements visible in this image?"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            "temperature": 0.3,
            "max_tokens": 300
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        print("\n" + "="*60)
        print("Testing text recognition...")
        
        response = requests.post(url, headers=headers, data=json.dumps(payload), timeout=90)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Text Recognition Test SUCCESSFUL!")
            
            if 'choices' in result and len(result['choices']) > 0:
                message = result['choices'][0]['message']['content']
                print(f"\nText Recognition: {message}")
            else:
                print("Unexpected response format")
        else:
            print("❌ Text Recognition FAILED!")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Text Recognition Error: {e}")

if __name__ == "__main__":
    test_complex_scene_analysis()
    test_text_recognition()
