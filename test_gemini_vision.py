#!/usr/bin/env python3

import google.generativeai as genai
from PIL import Image
import base64

# Test the Gemini API key with image analysis
api_key = "AIzaSyDU16bNRLaMg-Kcr36TTTDFZOpA4E61nMk"

try:
    # Configure the API key
    genai.configure(api_key=api_key)
    
    # Load the image
    image_path = "test_image.jpg"
    image = Image.open(image_path)
    
    print("Image loaded successfully!")
    print(f"Image size: {image.size}")
    print(f"Image mode: {image.mode}")
    
    print("\n" + "="*60)
    
    # Test 1: Basic image description
    print("TEST 1: Basic Image Description")
    print("-" * 40)
    
    model = genai.GenerativeModel('gemini-1.5-flash')
    response = model.generate_content([
        "Describe this image in detail. What do you see?",
        image
    ])
    
    print(f"Response: {response.text}")
    
    print("\n" + "="*60)
    
    # Test 2: Specific questions about the image
    print("TEST 2: Specific Questions")
    print("-" * 40)
    
    questions = [
        "What type of landscape is shown in this image?",
        "Are there any people visible in the image?",
        "What's the weather like in this photo?",
        "What colors dominate this image?",
        "Is this a natural or urban environment?"
    ]
    
    for i, question in enumerate(questions, 1):
        response = model.generate_content([question, image])
        print(f"Q{i}: {question}")
        print(f"A{i}: {response.text}")
        print()
    
    print("="*60)
    
    # Test 3: Creative interpretation
    print("TEST 3: Creative Interpretation")
    print("-" * 40)
    
    response = model.generate_content([
        "Write a short poem inspired by this image. Capture the mood and atmosphere.",
        image
    ])
    
    print(f"Poem:\n{response.text}")
    
    print("\n" + "="*60)
    
    # Test 4: Technical analysis
    print("TEST 4: Technical Analysis")
    print("-" * 40)
    
    response = model.generate_content([
        "Analyze this image from a photography perspective. Comment on composition, lighting, and visual elements.",
        image
    ])
    
    print(f"Photography Analysis:\n{response.text}")
    
    print("\n✅ Gemini Vision API Test SUCCESSFUL!")
    print("The model can successfully interpret and analyze images!")
    
except Exception as e:
    print("❌ Gemini Vision API Test FAILED!")
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
