#!/usr/bin/env python3

import requests
import json
import time
import statistics
import base64
from PIL import Image

# Test LM Studio local model with timing measurements
base_url = "http://127.0.0.1:1234"

def encode_image(image_path):
    """Encode image to base64"""
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def time_request(func, *args, **kwargs):
    """Time a function call and return result with timing"""
    start_time = time.time()
    result = func(*args, **kwargs)
    end_time = time.time()
    duration = end_time - start_time
    return result, duration

def test_text_response_timing():
    """Test basic text response with timing"""
    
    url = f"{base_url}/v1/chat/completions"
    
    test_prompts = [
        "Hello! How are you?",
        "What is the capital of France?",
        "Write a short poem about the ocean.",
        "Explain what artificial intelligence is in one sentence.",
        "What is 15 + 27?"
    ]
    
    print("="*70)
    print("TEXT RESPONSE TIMING TESTS")
    print("="*70)
    
    times = []
    
    for i, prompt in enumerate(test_prompts, 1):
        payload = {
            "model": "llava-1.6-mistral-7b",
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.7,
            "max_tokens": 100
        }
        
        headers = {"Content-Type": "application/json"}
        
        try:
            print(f"\nTest {i}: {prompt}")
            
            start_time = time.time()
            response = requests.post(url, headers=headers, data=json.dumps(payload), timeout=60)
            end_time = time.time()
            
            duration = end_time - start_time
            times.append(duration)
            
            if response.status_code == 200:
                result = response.json()
                if 'choices' in result:
                    message = result['choices'][0]['message']['content']
                    tokens = result.get('usage', {}).get('total_tokens', 'N/A')
                    print(f"✅ Response time: {duration:.2f}s | Tokens: {tokens}")
                    print(f"Response: {message[:100]}{'...' if len(message) > 100 else ''}")
                else:
                    print(f"❌ Unexpected response format")
            else:
                print(f"❌ Failed with status {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    if times:
        print(f"\n{'='*70}")
        print("TEXT TIMING STATISTICS:")
        print(f"{'='*70}")
        print(f"Average response time: {statistics.mean(times):.2f}s")
        print(f"Median response time: {statistics.median(times):.2f}s")
        print(f"Min response time: {min(times):.2f}s")
        print(f"Max response time: {max(times):.2f}s")
        if len(times) > 1:
            print(f"Standard deviation: {statistics.stdev(times):.2f}s")
    
    return times

def test_vision_response_timing():
    """Test vision response with timing"""
    
    url = f"{base_url}/v1/chat/completions"
    
    vision_prompts = [
        "What do you see in this image?",
        "Describe the colors in this image.",
        "What is the weather like in this photo?",
        "Are there any people in this image?"
    ]
    
    print(f"\n{'='*70}")
    print("VISION RESPONSE TIMING TESTS")
    print("="*70)
    
    try:
        # Load and encode image
        image_path = "test_image.jpg"
        base64_image = encode_image(image_path)
        print(f"Testing with image: {image_path}")
        
        times = []
        
        for i, prompt in enumerate(vision_prompts, 1):
            payload = {
                "model": "llava-1.6-mistral-7b",
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}
                            }
                        ]
                    }
                ],
                "temperature": 0.7,
                "max_tokens": 150
            }
            
            headers = {"Content-Type": "application/json"}
            
            try:
                print(f"\nVision Test {i}: {prompt}")
                
                start_time = time.time()
                response = requests.post(url, headers=headers, data=json.dumps(payload), timeout=90)
                end_time = time.time()
                
                duration = end_time - start_time
                times.append(duration)
                
                if response.status_code == 200:
                    result = response.json()
                    if 'choices' in result:
                        message = result['choices'][0]['message']['content']
                        tokens = result.get('usage', {}).get('total_tokens', 'N/A')
                        print(f"✅ Response time: {duration:.2f}s | Tokens: {tokens}")
                        print(f"Response: {message[:100]}{'...' if len(message) > 100 else ''}")
                    else:
                        print(f"❌ Unexpected response format")
                else:
                    print(f"❌ Failed with status {response.status_code}")
                    
            except Exception as e:
                print(f"❌ Error: {e}")
        
        if times:
            print(f"\n{'='*70}")
            print("VISION TIMING STATISTICS:")
            print(f"{'='*70}")
            print(f"Average response time: {statistics.mean(times):.2f}s")
            print(f"Median response time: {statistics.median(times):.2f}s")
            print(f"Min response time: {min(times):.2f}s")
            print(f"Max response time: {max(times):.2f}s")
            if len(times) > 1:
                print(f"Standard deviation: {statistics.stdev(times):.2f}s")
        
        return times
        
    except Exception as e:
        print(f"❌ Vision timing test error: {e}")
        return []

def test_complex_vision_timing():
    """Test complex vision with timing"""
    
    url = f"{base_url}/v1/chat/completions"
    
    try:
        # Load complex city image
        image_path = "city_image.jpg"
        base64_image = encode_image(image_path)
        
        payload = {
            "model": "llava-1.6-mistral-7b",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text", 
                            "text": "Analyze this urban scene in detail. What location is this? Describe everything you can see."
                        },
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}
                        }
                    ]
                }
            ],
            "temperature": 0.7,
            "max_tokens": 300
        }
        
        headers = {"Content-Type": "application/json"}
        
        print(f"\n{'='*70}")
        print("COMPLEX VISION TIMING TEST")
        print("="*70)
        print(f"Testing with complex image: {image_path}")
        
        start_time = time.time()
        response = requests.post(url, headers=headers, data=json.dumps(payload), timeout=120)
        end_time = time.time()
        
        duration = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            if 'choices' in result:
                message = result['choices'][0]['message']['content']
                tokens = result.get('usage', {}).get('total_tokens', 'N/A')
                print(f"✅ Complex vision response time: {duration:.2f}s | Tokens: {tokens}")
                print(f"Response: {message[:200]}{'...' if len(message) > 200 else ''}")
            else:
                print(f"❌ Unexpected response format")
        else:
            print(f"❌ Failed with status {response.status_code}")
        
        return duration
        
    except Exception as e:
        print(f"❌ Complex vision timing error: {e}")
        return None

if __name__ == "__main__":
    print("STARTING LM STUDIO PERFORMANCE TESTS...")
    print(f"Testing model at: {base_url}")
    print(f"Start time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run all timing tests
    text_times = test_text_response_timing()
    vision_times = test_vision_response_timing()
    complex_time = test_complex_vision_timing()
    
    # Overall summary
    print(f"\n{'='*70}")
    print("OVERALL PERFORMANCE SUMMARY")
    print("="*70)
    
    if text_times:
        print(f"Text responses - Avg: {statistics.mean(text_times):.2f}s")
    if vision_times:
        print(f"Vision responses - Avg: {statistics.mean(vision_times):.2f}s")
    if complex_time:
        print(f"Complex vision - Time: {complex_time:.2f}s")
    
    all_times = text_times + vision_times
    if complex_time:
        all_times.append(complex_time)
    
    if all_times:
        print(f"Overall average: {statistics.mean(all_times):.2f}s")
        print(f"Total tests completed: {len(all_times)}")
    
    print(f"Test completed at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
