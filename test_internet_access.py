#!/usr/bin/env python3

import requests
import json

# Test if LM Studio model has internet access
base_url = "http://127.0.0.1:1234"

def test_internet_knowledge():
    """Test if model has real-time internet access"""
    
    url = f"{base_url}/v1/chat/completions"
    
    test_questions = [
        "What is today's date?",
        "What are the latest news headlines?",
        "What is the current weather in New York?",
        "What is the current price of Bitcoin?",
        "What happened in the world today?"
    ]
    
    print("="*60)
    print("TESTING INTERNET ACCESS OF LOCAL MODEL")
    print("="*60)
    
    for i, question in enumerate(test_questions, 1):
        payload = {
            "model": "llava-1.6-mistral-7b",
            "messages": [{"role": "user", "content": question}],
            "temperature": 0.3,
            "max_tokens": 150
        }
        
        try:
            print(f"\nTest {i}: {question}")
            
            response = requests.post(url, headers={"Content-Type": "application/json"}, 
                                   data=json.dumps(payload), timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if 'choices' in result:
                    answer = result['choices'][0]['message']['content']
                    print(f"Answer: {answer}")
                else:
                    print("❌ Unexpected response format")
            else:
                print(f"❌ Failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print(f"\n{'='*60}")
    print("CONCLUSION:")
    print("❌ Local model has NO real-time internet access")
    print("✅ This is expected - local models are offline by design")
    print("🔧 Solution: Integrate with MCP servers for internet capabilities")

if __name__ == "__main__":
    test_internet_knowledge()
