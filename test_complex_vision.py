#!/usr/bin/env python3

import google.generativeai as genai
from PIL import Image

# Test the Gemini API key with a complex urban image
api_key = "AIzaSyDU16bNRLaMg-Kcr36TTTDFZOpA4E61nMk"

try:
    # Configure the API key
    genai.configure(api_key=api_key)
    
    # Load the complex city image
    image_path = "city_image.jpg"
    image = Image.open(image_path)
    
    print("Complex city image loaded successfully!")
    print(f"Image size: {image.size}")
    
    print("\n" + "="*60)
    
    # Test complex image analysis
    model = genai.GenerativeModel('gemini-1.5-flash')
    
    # Test 1: Detailed scene analysis
    print("TEST: Complex Scene Analysis")
    print("-" * 40)
    
    response = model.generate_content([
        "Analyze this image in detail. What location is this? What can you see? Count any visible signs, people, vehicles, or other notable elements.",
        image
    ])
    
    print(f"Analysis: {response.text}")
    
    print("\n" + "="*60)
    
    # Test 2: Text reading in image
    print("TEST: Text Recognition")
    print("-" * 40)
    
    response = model.generate_content([
        "Read and list all the text you can see in this image, including signs, advertisements, and any other written content.",
        image
    ])
    
    print(f"Text Recognition: {response.text}")
    
    print("\n✅ Complex Vision Analysis SUCCESSFUL!")
    
except Exception as e:
    print("❌ Complex Vision Analysis FAILED!")
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
