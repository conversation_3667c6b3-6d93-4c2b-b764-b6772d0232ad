#!/usr/bin/env python3

import requests
import json
import time
import statistics

# Test LM Studio local model - basic text response with timing
base_url = "http://127.0.0.1:1234"

def test_basic_response():
    """Test basic text response from LM Studio model"""
    
    # LM Studio typically uses OpenAI-compatible API
    url = f"{base_url}/v1/chat/completions"
    
    payload = {
        "model": "llava-1.6-mistral-7b",  # This might need to be adjusted based on LM Studio setup
        "messages": [
            {
                "role": "user",
                "content": "Hello! Can you introduce yourself? What model are you?"
            }
        ],
        "temperature": 0.7,
        "max_tokens": 150
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print("Testing LM Studio basic text response...")
        print(f"URL: {url}")
        
        response = requests.post(url, headers=headers, data=json.dumps(payload), timeout=30)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ LM Studio Basic Test SUCCESSFUL!")
            
            if 'choices' in result and len(result['choices']) > 0:
                message = result['choices'][0]['message']['content']
                print(f"\nModel Response: {message}")
                
                # Print some additional info if available
                if 'model' in result:
                    print(f"Model: {result['model']}")
                if 'usage' in result:
                    print(f"Usage: {result['usage']}")
                    
            else:
                print(f"Unexpected response format: {result}")
                
        else:
            print("❌ LM Studio Basic Test FAILED!")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error - Is LM Studio running on http://127.0.0.1:1234?")
    except requests.exceptions.Timeout:
        print("❌ Timeout Error - Model might be taking too long to respond")
    except Exception as e:
        print(f"❌ Error: {e}")

def test_model_info():
    """Test getting model information"""
    
    url = f"{base_url}/v1/models"
    
    try:
        print("\nTesting model information endpoint...")
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Model Info Retrieved!")
            print(f"Available models: {json.dumps(result, indent=2)}")
        else:
            print(f"❌ Model info failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Model info error: {e}")

if __name__ == "__main__":
    test_model_info()
    print("\n" + "="*60)
    test_basic_response()
