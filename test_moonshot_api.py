#!/usr/bin/env python3

import os
from openai import OpenAI

# Test the new API key
api_key = "sk-d6KgwCklE8xd1UEcLIcMiPeQ6PzFHbyVGkiqZn8v65u4w1tt"

client = OpenAI(
    api_key=api_key,
    base_url="https://api.moonshot.ai/v1",
)

try:
    # First test with a simple request
    print("Testing simple chat completion...")
    completion = client.chat.completions.create(
        model="moonshot-v1-8k",
        messages=[
            {"role": "user", "content": "Hello, just say hi back"}
        ],
        max_tokens=10,
        temperature=0.3,
    )
    
    print("✅ API Key Test SUCCESSFUL!")
    print("\nResponse:")
    print(completion.choices[0].message)
    
except Exception as e:
    print("❌ API Key Test FAILED!")
    print(f"Error: {e}")
