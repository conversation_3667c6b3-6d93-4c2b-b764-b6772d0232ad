#!/usr/bin/env python3

import requests
import json
import base64

# Test Gemini Vision with REST API
api_key = "AIzaSyDU16bNRLaMg-Kcr36TTTDFZOpA4E61nMk"

try:
    # Read and encode the image
    with open("test_image.jpg", "rb") as image_file:
        image_data = base64.b64encode(image_file.read()).decode('utf-8')
    
    # Gemini Vision API endpoint
    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={api_key}"
    
    # Payload with image and text
    payload = {
        "contents": [
            {
                "parts": [
                    {
                        "text": "What season do you think this photo was taken in? Explain your reasoning based on visual clues."
                    },
                    {
                        "inline_data": {
                            "mime_type": "image/jpeg",
                            "data": image_data
                        }
                    }
                ]
            }
        ]
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    print("Testing Gemini Vision with REST API...")
    response = requests.post(url, headers=headers, data=json.dumps(payload))
    
    if response.status_code == 200:
        result = response.json()
        if 'candidates' in result and len(result['candidates']) > 0:
            text_response = result['candidates'][0]['content']['parts'][0]['text']
            print("✅ REST API Vision Test SUCCESSFUL!")
            print(f"\nResponse: {text_response}")
        else:
            print(f"Unexpected response format: {result}")
    else:
        print("❌ REST API Vision Test FAILED!")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
except Exception as e:
    print("❌ REST API Vision Test FAILED!")
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
