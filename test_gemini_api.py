#!/usr/bin/env python3

import requests
import json

# Test the Gemini API key
api_key = "AIzaSyDU16bNRLaMg-Kcr36TTTDFZOpA4E61nMk"

# Gemini API endpoint
url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key={api_key}"

# Test payload
payload = {
    "contents": [
        {
            "parts": [
                {
                    "text": "Hello! This is a test message. Please respond with a simple greeting."
                }
            ]
        }
    ]
}

headers = {
    "Content-Type": "application/json"
}

try:
    print("Testing Gemini API key...")
    response = requests.post(url, headers=headers, data=json.dumps(payload))
    
    if response.status_code == 200:
        print("✅ Gemini API Key Test SUCCESSFUL!")
        result = response.json()
        if 'candidates' in result and len(result['candidates']) > 0:
            text_response = result['candidates'][0]['content']['parts'][0]['text']
            print(f"\nResponse: {text_response}")
        else:
            print(f"\nFull response: {result}")
    else:
        print("❌ Gemini API Key Test FAILED!")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
except Exception as e:
    print("❌ Gemini API Key Test FAILED!")
    print(f"Error: {e}")
