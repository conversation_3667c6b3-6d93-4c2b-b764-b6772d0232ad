{
      "github": {
      "command": "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-github"
      ],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"
      }
    },
      "Context7": {
      "type": "stdio",
      "command": "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/npx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    },
      "perplexity-ask": {
        "command": "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/npx",
        "args": [
          "-y",
          "server-perplexity-ask"
        ],
        "env": {
          "PERPLEXITY_API_KEY": "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"
        }
      },
      "The21stDevMagic": {
        "args": [
          "-y",
          "@21st-dev/magic@latest",
          "--api-key",
          "77f99f24b81696d73870b6378c626ef9753fa56334f66261847aa6facbb3f357"
        ],
        "command": "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/npx"
      },
      "apify-actors": {
        "args": [
          "-y",
          "@apify/actors-mcp-server",
          "--actors",
          "lukaskrivka/google-maps-with-contact-details,apify/instagram-scraper"
        ],
        "command": "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/npx",
        "env": {
          "APIFY_TOKEN": "**********************************************"
        }
      },
      "brave-search": {
        "args": [
          "-y",
          "@modelcontextprotocol/server-brave-search"
        ],
        "command": "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/npx",
        "env": {
          "BRAVE_API_KEY": "BSAIiGF-k5xVMKf7_Pl791FvvEdJyCY"
        }
      },
      "firecrawl-mcp": {
        "args": [
          "-y",
          "firecrawl-mcp"
        ],
        "command": "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/npx",
        "env": {
          "FIRECRAWL_API_KEY": "fc-2cd161292bbf455da0de03784e7723fd"
        }
      },
      "heroku": {
        "args": [
          "-y",
          "@heroku/mcp-server"
        ],
        "command": "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/npx",
        "env": {
          "HEROKU_API_KEY": "HRKU-************************************"
        }
      },
      "mongodb": {
        "args": [
          "-y",
          "mongodb-lens@latest",
          "mongodb+srv://hajirufai:<EMAIL>/"
        ],
        "command": "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/npx"
      },
      "neon": {
        "args": [
          "-y",
          "@neondatabase/mcp-server-neon",
          "start",
          "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
        ],
        "command": "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/npx"
      },
      "tavily-mcp": {
        "args": [
          "-y",
          "tavily-mcp@0.1.4"
        ],
        "autoApprove": [],
        "command": "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/npx",
        "disabled": false,
        "env": {
          "TAVILY_API_KEY": "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"
        }
      },
      "shrimp-task-manager": {
        "command": "/Users/<USER>/.nvm/versions/node/v20.19.0/bin/npx",
        "args": [
          "-y",
          "mcp-shrimp-task-manager"
        ],
        "env": {
          "DATA_DIR": "/Users/<USER>/mcp-shrimp-task-manager-storage-data" // 必須使用絕對路徑
        }
      }
    }