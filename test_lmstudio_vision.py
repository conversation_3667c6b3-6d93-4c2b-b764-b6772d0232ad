#!/usr/bin/env python3

import requests
import json
import base64
from PIL import Image

# Test LM Studio local model - vision capabilities
base_url = "http://127.0.0.1:1234"

def encode_image(image_path):
    """Encode image to base64"""
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def test_vision_capabilities():
    """Test vision capabilities with the nature image"""
    
    url = f"{base_url}/v1/chat/completions"
    
    try:
        # Load and encode the image
        image_path = "test_image.jpg"
        image = Image.open(image_path)
        print(f"Testing with image: {image_path}")
        print(f"Image size: {image.size}")
        
        # Encode image to base64
        base64_image = encode_image(image_path)
        
        payload = {
            "model": "llava-1.6-mistral-7b",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "What do you see in this image? Describe it in detail."
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            "temperature": 0.7,
            "max_tokens": 300
        }
        
        headers = {
            "Content-Type": "application/json"
        }
        
        print("\nTesting LM Studio vision capabilities...")
        print("Sending image for analysis...")
        
        response = requests.post(url, headers=headers, data=json.dumps(payload), timeout=60)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ LM Studio Vision Test SUCCESSFUL!")
            
            if 'choices' in result and len(result['choices']) > 0:
                message = result['choices'][0]['message']['content']
                print(f"\nVision Analysis: {message}")
                
                if 'usage' in result:
                    print(f"\nUsage: {result['usage']}")
                    
            else:
                print(f"Unexpected response format: {result}")
                
        else:
            print("❌ LM Studio Vision Test FAILED!")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Vision Test Error: {e}")
        import traceback
        traceback.print_exc()

def test_vision_specific_questions():
    """Test with specific questions about the image"""
    
    url = f"{base_url}/v1/chat/completions"
    
    try:
        image_path = "test_image.jpg"
        base64_image = encode_image(image_path)
        
        questions = [
            "What type of path or walkway is shown in this image?",
            "What is the weather like in this photo?",
            "Are there any people visible in the image?",
            "What colors dominate this scene?"
        ]
        
        print("\n" + "="*60)
        print("Testing specific vision questions...")
        
        for i, question in enumerate(questions, 1):
            payload = {
                "model": "llava-1.6-mistral-7b",
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": question
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                "temperature": 0.3,
                "max_tokens": 150
            }
            
            headers = {
                "Content-Type": "application/json"
            }
            
            print(f"\nQ{i}: {question}")
            
            response = requests.post(url, headers=headers, data=json.dumps(payload), timeout=45)
            
            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    answer = result['choices'][0]['message']['content']
                    print(f"A{i}: {answer}")
                else:
                    print(f"A{i}: Unexpected response format")
            else:
                print(f"A{i}: Error - {response.status_code}")
                
    except Exception as e:
        print(f"❌ Specific Questions Error: {e}")

if __name__ == "__main__":
    test_vision_capabilities()
    test_vision_specific_questions()
