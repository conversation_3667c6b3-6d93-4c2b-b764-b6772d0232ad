#!/usr/bin/env python3

import requests
import json

# Summary of LM Studio testing results
base_url = "http://127.0.0.1:1234"

def test_model_capabilities():
    """Test various capabilities of the LM Studio model"""
    
    print("="*70)
    print("LM STUDIO LLAVA-1.6-MISTRAL-7B TEST SUMMARY")
    print("="*70)
    
    # Test 1: Model Information
    print("\n1. MODEL INFORMATION:")
    print("-" * 30)
    
    try:
        response = requests.get(f"{base_url}/v1/models", timeout=10)
        if response.status_code == 200:
            models = response.json()
            print("✅ Model endpoint accessible")
            print(f"Available models: {len(models['data'])}")
            for model in models['data']:
                print(f"  - {model['id']}")
        else:
            print("❌ Model endpoint failed")
    except Exception as e:
        print(f"❌ Model endpoint error: {e}")
    
    # Test 2: Basic Text Generation
    print("\n2. TEXT GENERATION:")
    print("-" * 30)
    
    try:
        payload = {
            "model": "llava-1.6-mistral-7b",
            "messages": [{"role": "user", "content": "Write a haiku about artificial intelligence."}],
            "temperature": 0.7,
            "max_tokens": 100
        }
        
        response = requests.post(f"{base_url}/v1/chat/completions", 
                               headers={"Content-Type": "application/json"}, 
                               data=json.dumps(payload), timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Text generation working")
            if 'choices' in result:
                print(f"Response: {result['choices'][0]['message']['content']}")
        else:
            print("❌ Text generation failed")
            
    except Exception as e:
        print(f"❌ Text generation error: {e}")
    
    # Test 3: Reasoning
    print("\n3. REASONING CAPABILITIES:")
    print("-" * 30)
    
    try:
        payload = {
            "model": "llava-1.6-mistral-7b",
            "messages": [{"role": "user", "content": "If I have 3 apples and give away 1, then buy 2 more, how many do I have? Show your work."}],
            "temperature": 0.3,
            "max_tokens": 150
        }
        
        response = requests.post(f"{base_url}/v1/chat/completions", 
                               headers={"Content-Type": "application/json"}, 
                               data=json.dumps(payload), timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Reasoning capabilities working")
            if 'choices' in result:
                print(f"Response: {result['choices'][0]['message']['content']}")
        else:
            print("❌ Reasoning test failed")
            
    except Exception as e:
        print(f"❌ Reasoning test error: {e}")
    
    # Test 4: Code Generation
    print("\n4. CODE GENERATION:")
    print("-" * 30)
    
    try:
        payload = {
            "model": "llava-1.6-mistral-7b",
            "messages": [{"role": "user", "content": "Write a simple Python function to calculate the factorial of a number."}],
            "temperature": 0.3,
            "max_tokens": 200
        }
        
        response = requests.post(f"{base_url}/v1/chat/completions", 
                               headers={"Content-Type": "application/json"}, 
                               data=json.dumps(payload), timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Code generation working")
            if 'choices' in result:
                print(f"Response: {result['choices'][0]['message']['content']}")
        else:
            print("❌ Code generation failed")
            
    except Exception as e:
        print(f"❌ Code generation error: {e}")
    
    print("\n" + "="*70)
    print("VISION CAPABILITIES SUMMARY:")
    print("="*70)
    print("✅ Basic image description - WORKING")
    print("✅ Scene analysis - WORKING") 
    print("✅ Object identification - WORKING")
    print("✅ Color recognition - WORKING")
    print("✅ Complex urban scenes - WORKING")
    print("✅ Text recognition (basic) - WORKING")
    print("✅ Atmosphere/mood detection - WORKING")
    
    print("\n" + "="*70)
    print("PERFORMANCE NOTES:")
    print("="*70)
    print("• Model responds relatively quickly for local inference")
    print("• Vision processing works with base64 encoded images")
    print("• Supports OpenAI-compatible API format")
    print("• Good at scene description and basic object recognition")
    print("• Can identify famous locations (Times Square)")
    print("• Handles both simple and complex visual scenes")
    
    print("\n" + "="*70)
    print("COMPARISON WITH CLOUD MODELS:")
    print("="*70)
    print("Advantages:")
    print("• ✅ Runs locally - no API costs")
    print("• ✅ Privacy - data stays on your machine") 
    print("• ✅ No rate limits")
    print("• ✅ Works offline")
    
    print("\nLimitations compared to Gemini:")
    print("• ⚠️  Less detailed text recognition")
    print("• ⚠️  Shorter, less comprehensive responses")
    print("• ⚠️  May miss some fine details")
    print("• ⚠️  Limited by local hardware performance")

if __name__ == "__main__":
    test_model_capabilities()
